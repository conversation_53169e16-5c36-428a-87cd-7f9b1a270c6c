import simpleRestProvider from 'ra-data-simple-rest';
import { DataProvider, fetchUtils } from 'react-admin';

const apiUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000';

// Custom httpClient to handle JWT authentication
const httpClient = (url: string, options: fetchUtils.Options = {}) => {
    if (!options.headers) {
        options.headers = new Headers({ Accept: 'application/json' });
    }

    // Add JWT token to requests
    const token = localStorage.getItem('token');
    if (token) {
        (options.headers as Headers).set('Authorization', `Bearer ${token}`);
    }

    return fetchUtils.fetchJson(url, options);
};

// Create the base data provider
const baseDataProvider = simpleRestProvider(`${apiUrl}/api`, httpClient);

// Custom data provider to handle backend API structure
const dataProvider: DataProvider = {
    ...baseDataProvider,

    // Override getList to handle backend pagination format
    getList: (resource, params) => {
        // Handle mock categories since backend doesn't have them
        if (resource === 'categories') {
            return Promise.resolve({
                data: [
                    { id: 1, name: 'Laptops' },
                    { id: 2, name: 'Electronics' },
                ],
                total: 2,
            });
        }

        const { page, perPage } = params.pagination;
        const { field, order } = params.sort;
        const query = {
            page: page,
            limit: perPage,
            sort_by: field,
            sort_order: order.toLowerCase(),
            ...params.filter,
        };

        const url = `${apiUrl}/api/${resource}?${new URLSearchParams(query).toString()}`;

        return httpClient(url).then(({ json }) => {
            console.log('API Response:', json); // Debug log

            let data = json.data || json[resource] || json.products || json.customers || json.orders || json.inventory || json.containers || [];

            // Transform backend data to match frontend expectations
            if (resource === 'products' && json.products) {
                data = json.products.map((product: any) => ({
                    id: product.id,
                    category_id: 1, // Default category since backend doesn't have categories
                    reference: `${product.brand} ${product.model}`,
                    width: 20, // Default dimensions
                    height: 30,
                    price: parseFloat(product.base_price || '0'),
                    thumbnail: product.image_url || 'https://via.placeholder.com/150',
                    image: product.image_url || 'https://via.placeholder.com/300',
                    description: JSON.stringify(product.specifications_json || {}),
                    stock: product.inventory_summary?.available_items || 0,
                    sales: product.inventory_summary?.sold_items || 0,
                    // Keep original data for reference
                    _original: product
                }));
            }

            return {
                data: data,
                total: json.total || json.pagination?.total_items || json.pagination?.total || 0,
            };
        });
    },

    // Override getOne to handle backend response format
    getOne: (resource, params) =>
        httpClient(`${apiUrl}/api/${resource}/${params.id}`).then(({ json }) => {
            let data = json.data || json[resource.slice(0, -1)] || json;

            // Transform backend data to match frontend expectations
            if (resource === 'products' && data) {
                data = {
                    id: data.id,
                    category_id: 1,
                    reference: `${data.brand} ${data.model}`,
                    width: 20,
                    height: 30,
                    price: parseFloat(data.base_price || '0'),
                    thumbnail: data.image_url || 'https://via.placeholder.com/150',
                    image: data.image_url || 'https://via.placeholder.com/300',
                    description: JSON.stringify(data.specifications_json || {}),
                    stock: data.inventory_summary?.available_items || 0,
                    sales: data.inventory_summary?.sold_items || 0,
                    _original: data
                };
            }

            return { data };
        }),

    // Override create to handle backend response format
    create: (resource, params) =>
        httpClient(`${apiUrl}/api/${resource}`, {
            method: 'POST',
            body: JSON.stringify(params.data),
        }).then(({ json }) => ({
            data: { ...params.data, id: json.data?.id || json.id },
        })),

    // Override update to handle backend response format
    update: (resource, params) =>
        httpClient(`${apiUrl}/api/${resource}/${params.id}`, {
            method: 'PUT',
            body: JSON.stringify(params.data),
        }).then(({ json }) => ({
            data: json.data || json,
        })),

    // Override delete to handle backend response format
    delete: (resource, params) =>
        httpClient(`${apiUrl}/api/${resource}/${params.id}`, {
            method: 'DELETE',
        }).then(({ json }) => ({
            data: json.data || { id: params.id },
        })),
};

export default dataProvider;

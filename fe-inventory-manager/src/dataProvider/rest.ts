import simpleRestProvider from 'ra-data-simple-rest';
import { DataProvider, fetchUtils } from 'react-admin';

const apiUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000';

// Custom httpClient to handle JWT authentication
const httpClient = (url: string, options: fetchUtils.Options = {}) => {
    if (!options.headers) {
        options.headers = new Headers({ Accept: 'application/json' });
    }

    // Add JWT token to requests
    const token = localStorage.getItem('token');
    if (token) {
        (options.headers as Headers).set('Authorization', `Bearer ${token}`);
    }

    return fetchUtils.fetchJson(url, options);
};

// Create the base data provider
const baseDataProvider = simpleRestProvider(`${apiUrl}/api`, httpClient);

// Custom data provider to handle backend API structure
const dataProvider: DataProvider = {
    ...baseDataProvider,

    // Override getList to handle backend pagination format
    getList: (resource, params) => {
        const { page, perPage } = params.pagination;
        const { field, order } = params.sort;
        const query = {
            page: page,
            limit: perPage,
            sort_by: field,
            sort_order: order.toLowerCase(),
            ...params.filter,
        };

        const url = `${apiUrl}/api/${resource}?${new URLSearchParams(query).toString()}`;

        return httpClient(url).then(({ json }) => ({
            data: json.data || json[resource] || [],
            total: json.total || json.pagination?.total || 0,
        }));
    },

    // Override getOne to handle backend response format
    getOne: (resource, params) =>
        httpClient(`${apiUrl}/api/${resource}/${params.id}`).then(({ json }) => ({
            data: json.data || json[resource.slice(0, -1)] || json,
        })),

    // Override create to handle backend response format
    create: (resource, params) =>
        httpClient(`${apiUrl}/api/${resource}`, {
            method: 'POST',
            body: JSON.stringify(params.data),
        }).then(({ json }) => ({
            data: { ...params.data, id: json.data?.id || json.id },
        })),

    // Override update to handle backend response format
    update: (resource, params) =>
        httpClient(`${apiUrl}/api/${resource}/${params.id}`, {
            method: 'PUT',
            body: JSON.stringify(params.data),
        }).then(({ json }) => ({
            data: json.data || json,
        })),

    // Override delete to handle backend response format
    delete: (resource, params) =>
        httpClient(`${apiUrl}/api/${resource}/${params.id}`, {
            method: 'DELETE',
        }).then(({ json }) => ({
            data: json.data || { id: params.id },
        })),
};

export default dataProvider;

import {
  AccessDenied,
  AccessDeniedClasses,
  AddItemButton,
  AddSavedQueryDialog,
  AddSavedQueryIconButton,
  AdminContext,
  AdminUI,
  AppBar,
  AppBarClasses,
  ApplicationUpdatedNotification,
  ArrayField,
  ArrayInput,
  ArrayInputClasses,
  ArrayInputContext,
  AuthCallback,
  AuthError,
  AuthErrorClasses,
  AuthLayout,
  AuthLayoutClasses,
  AuthenticationError,
  AuthenticationErrorClasses,
  AutocompleteArrayInput,
  AutocompleteInput,
  AutocompleteInputClasses,
  BooleanField,
  BooleanInput,
  BulkActionsToolbar,
  BulkActionsToolbarClasses,
  BulkDeleteButton,
  BulkDeleteWithConfirmButton,
  BulkDeleteWithUndoButton,
  BulkExportButton,
  BulkUpdateButton,
  BulkUpdateWithConfirmButton,
  BulkUpdateWithUndoButton,
  Button,
  CLOSED_DRAWER_WIDTH,
  CardContentInner,
  CardContentInnerClasses,
  CheckForApplicationUpdate,
  CheckboxGroupInput,
  CheckboxGroupInputClasses,
  ChipField,
  CloneButton_default,
  ColumnsButton,
  ColumnsSelector,
  ColumnsSelectorItem,
  Configurable,
  ConfigurableClasses,
  Confirm,
  ConfirmClasses,
  Count,
  Create,
  CreateActions,
  CreateButtonClasses,
  CreateButton_default,
  CreateClasses,
  CreateView,
  DRAWER_WIDTH,
  DashboardMenuItem,
  DataTable,
  DataTableBody,
  DataTableCell,
  DataTableClasses,
  DataTableColumn,
  DataTableHead,
  DataTableHeadCell,
  DataTableLoading,
  DataTableNumberColumn,
  DataTableRoot,
  DataTableRow,
  DataTableRowSxContext,
  Datagrid,
  DatagridBody_default,
  DatagridCell_default,
  DatagridClasses,
  DatagridConfigurable,
  DatagridHeader,
  DatagridHeaderCellClasses,
  DatagridHeaderCell_default,
  DatagridInput,
  DatagridLoading_default,
  DatagridPrefix,
  DatagridRoot,
  DatagridRow_default,
  DateField,
  DateInput,
  DateTimeInput,
  DeleteButton,
  DeleteWithConfirmButton,
  DeleteWithUndoButton,
  DeviceTestWrapper,
  Edit,
  EditActions,
  EditButton,
  EditButtonClasses,
  EditClasses,
  EditGuesser,
  EditView,
  EmailField,
  Empty,
  EmptyClasses,
  Error,
  ErrorClasses,
  ExpandRowButton_default,
  ExportButton,
  FieldToggle,
  FieldsSelector,
  FileField,
  FileInput,
  FileInputClasses,
  Filter,
  FilterButton,
  FilterButtonMenuItem,
  FilterClasses,
  FilterContext,
  FilterForm,
  FilterFormBase,
  FilterFormClasses,
  FilterFormInput,
  FilterFormInputClasses,
  FilterList,
  FilterListItem,
  FilterListItemClasses,
  FilterListSection,
  FilterLiveSearch,
  FormTab,
  FormTabHeader,
  FunctionField,
  HideOnScroll,
  IconButtonWithTooltip,
  ImageField,
  ImageFieldClasses,
  ImageInput,
  InPlaceEditor,
  InfiniteList,
  InfinitePagination,
  InputHelperText,
  Inspector,
  InspectorButton,
  InspectorClasses,
  InspectorRoot,
  Labeled,
  LabeledClasses,
  Layout,
  LayoutClasses,
  LinearProgress,
  Link,
  LinkClasses,
  List,
  ListActions,
  ListButton,
  ListClasses,
  ListGuesser,
  ListNoResults,
  ListToolbar,
  ListView,
  Loading,
  LoadingClasses,
  LoadingIndicator,
  LoadingIndicatorClasses,
  LoadingInput,
  LoadingPage,
  LocalesMenuButton,
  LocalesMenuButtonClasses,
  Login,
  LoginClasses,
  LoginForm,
  LoginFormClasses,
  LoginWithEmail,
  Logout,
  LogoutClasses,
  Menu,
  MenuClasses,
  MenuItemLink,
  MenuItemLinkClasses,
  NotFound,
  NotFoundClasses,
  Notification,
  NotificationClasses,
  NullableBooleanInput,
  NullableBooleanInputClasses,
  NumberField,
  NumberInput,
  PageTitleConfigurable,
  PageTitleEditor,
  Pagination,
  PaginationActions,
  PasswordInput,
  Placeholder,
  PrevNextButtonClasses,
  PrevNextButtons,
  PureDatagridBody,
  PureDatagridRow,
  RadioButtonGroupInput,
  RadioButtonGroupInputClasses,
  ReOrderButtons,
  ReferenceArrayField,
  ReferenceArrayFieldClasses,
  ReferenceArrayFieldView,
  ReferenceArrayInput,
  ReferenceError,
  ReferenceField,
  ReferenceFieldClasses,
  ReferenceFieldView,
  ReferenceInput,
  ReferenceManyCount,
  ReferenceManyField,
  ReferenceOneField,
  RefreshButton,
  RefreshIconButton,
  RemoveItemButton,
  RemoveSavedQueryDialog,
  RemoveSavedQueryIconButton,
  ResettableTextField,
  ResettableTextFieldClasses,
  ResettableTextFieldStyles,
  ResourceMenuItem,
  ResourceMenuItems,
  RichTextField,
  SaveButton,
  SavedQueriesList,
  SavedQueriesListClasses,
  SavedQueryFilterListItem,
  SavedQueryFilterListItemClasses,
  SearchInput,
  SelectAllButton,
  SelectArrayInput,
  SelectArrayInputClasses,
  SelectColumnsButton,
  SelectField,
  SelectInput,
  SelectPageCheckbox,
  SelectRowCheckbox,
  Show,
  ShowActions,
  ShowButton_default,
  ShowClasses,
  ShowGuesser,
  ShowView,
  Sidebar,
  SidebarClasses,
  SidebarToggleButton,
  SidebarToggleButtonClasses,
  SimpleForm,
  SimpleFormConfigurable,
  SimpleFormIterator,
  SimpleFormIteratorClasses,
  SimpleFormIteratorContext,
  SimpleFormIteratorItem,
  SimpleFormIteratorItemContext,
  SimpleFormIteratorPrefix,
  SimpleList,
  SimpleListClasses,
  SimpleListConfigurable,
  SimpleListLoading,
  SimpleListLoadingClasses,
  SimpleShowLayout,
  SimpleShowLayoutClasses,
  SingleFieldList,
  SingleFieldListClasses,
  SkipNavigationButton,
  SortButton_default,
  Tab,
  TabClasses,
  TabbedForm,
  TabbedFormClasses,
  TabbedFormTabs,
  TabbedFormView,
  TabbedShowLayout,
  TabbedShowLayoutClasses,
  TabbedShowLayoutTabs,
  TextArrayInput,
  TextField,
  TextInput,
  ThemeProvider,
  ThemesContext,
  TimeInput,
  Title,
  TitlePortal,
  ToggleThemeButton,
  Toolbar,
  ToolbarClasses,
  TopToolbar,
  TranslatableFields,
  TranslatableFieldsTab,
  TranslatableFieldsTabContent,
  TranslatableInputs,
  TranslatableInputsClasses,
  TranslatableInputsTab,
  TranslatableInputsTabClasses,
  TranslatableInputsTabContent,
  TranslatableInputsTabContentClasses,
  TranslatableInputsTabs,
  TranslatableInputsTabsClasses,
  UpdateButton,
  UpdateWithConfirmButton,
  UpdateWithUndoButton,
  UrlField,
  UserMenu,
  UserMenuClasses,
  UserMenuContext,
  WrapperField,
  areValidSavedQueries,
  bwDarkTheme,
  bwLightTheme,
  defaultDarkTheme,
  defaultLightTheme,
  defaultTheme,
  editFieldTypes,
  extractValidSavedQueries,
  findTabsWithErrors,
  getArrayInputError,
  getShowLayoutTabFullPath,
  getTabbedFormTabFullPath,
  houseDarkTheme,
  houseLightTheme,
  isValidSavedQuery,
  listFieldTypes,
  nanoDarkTheme,
  nanoLightTheme,
  radiantDarkTheme,
  radiantLightTheme,
  removeTags,
  sanitizeFieldRestProps,
  sanitizeInputRestProps,
  showFieldTypes,
  useArrayInput,
  useCreateSuggestionContext,
  useDataTableRowSxContext,
  useDatagridContext,
  useSavedQueries,
  useSidebarState,
  useSimpleFormIterator,
  useSimpleFormIteratorItem,
  useSupportCreateSuggestion,
  useTheme,
  useThemesContext,
  useUserMenu
} from "./chunk-MIRT2KO3.js";
import {
  AUTH_CHECK,
  AUTH_ERROR,
  AUTH_GET_PERMISSIONS,
  AUTH_LOGIN,
  AUTH_LOGOUT,
  AddNotificationContext,
  AddUndoableMutationContext,
  AdminRouter,
  AuthContext,
  Authenticated,
  BasenameContextProvider,
  CREATE,
  CanAccess,
  ChoicesContext,
  ChoicesContextProvider,
  CloseNotificationContext,
  CoreAdmin,
  CoreAdminContext,
  CoreAdminRoutes,
  CoreAdminUI,
  CreateBase,
  CreateContext,
  CreateContextProvider,
  CreateController,
  CustomRoutes,
  DEFAULT_LOCALE,
  DELETE,
  DELETE_MANY,
  DataProviderContext_default,
  DataTableBase,
  DataTableCallbacksContext,
  DataTableColumnRankContext,
  DataTableConfigContext,
  DataTableDataContext,
  DataTableRenderContext,
  DataTableSelectedIdsContext,
  DataTableSortContext,
  DataTableStoreContext,
  DefaultTitleContext,
  DefaultTitleContextProvider,
  EditBase,
  EditContext,
  EditContextProvider,
  EditController,
  ExporterContext,
  FieldTitle_default,
  FilterLiveForm,
  Form,
  FormDataConsumer,
  FormDataConsumerView,
  FormGroupContext,
  FormGroupContextProvider,
  FormGroupsProvider,
  GET_LIST,
  GET_MANY,
  GET_MANY_REFERENCE,
  GET_ONE,
  HIDE_FILTER,
  HasDashboardContext,
  HasDashboardContextProvider,
  HttpError_default,
  I18N_CHANGE_LOCALE,
  I18N_TRANSLATE,
  I18nContext,
  I18nContextProvider,
  InferenceTypes,
  InferredElement_default,
  InfiniteListBase,
  InfinitePaginationContext,
  ListBase,
  ListContext,
  ListContextProvider,
  ListController,
  ListFilterContext,
  ListPaginationContext,
  ListSortContext,
  LogoutOnMount,
  NavigateToFirstResource,
  NotificationContext,
  NotificationContextProvider,
  OptionalRecordContextProvider,
  OptionalResourceContextProvider,
  PreferenceKeyContext,
  PreferenceKeyContextProvider,
  PreferencesEditorContext,
  PreferencesEditorContextProvider,
  PreviousLocationStorageKey,
  Ready_default,
  RecordContext,
  RecordContextProvider,
  RecordRepresentation,
  ReferenceFieldBase,
  ReferenceFieldContext,
  ReferenceFieldContextProvider,
  ReferenceInputBase,
  Resource,
  ResourceContext,
  ResourceContextProvider,
  ResourceDefinitionContext,
  ResourceDefinitionContextProvider,
  RestoreScrollPosition,
  SET_FILTER,
  SET_PAGE,
  SET_PER_PAGE,
  SET_SORT,
  SHOW_FILTER,
  SORT_ASC,
  SORT_DESC,
  SaveContext,
  SaveContextProvider,
  ShowBase,
  ShowContext,
  ShowContextProvider,
  ShowController,
  SourceContext,
  SourceContextProvider,
  StoreContext,
  StoreContextProvider,
  StoreSetter,
  TakeUndoableMutationContext,
  TestMemoryRouter,
  TestTranslationProvider,
  TranslatableContext,
  TranslatableContextProvider,
  Translate,
  UPDATE,
  UPDATE_MANY,
  UndoableMutationsContextProvider,
  ValidationError,
  WarnWhenUnsavedChanges,
  WithListContext,
  WithPermissions_default,
  WithRecord,
  addRefreshAuthToAuthProvider,
  addRefreshAuthToDataProvider,
  applyCallbacks,
  asyncDebounce,
  choices,
  combine2Validators,
  combineDataProviders,
  composeSyncValidators,
  composeValidators,
  convertLegacyAuthProvider_default,
  convertLegacyDataProvider_default,
  defaultDataProvider,
  defaultExporter,
  downloadCSV,
  email,
  escapePath_default,
  fetchActionsWithArrayOfIdentifiedRecordsResponse,
  fetchActionsWithArrayOfRecordsResponse,
  fetchActionsWithRecordResponse,
  fetchActionsWithTotalResponse,
  fetchRelatedRecords,
  fetch_exports,
  getElementsFromRecords_default,
  getFieldLabelTranslationArgs,
  getFilterFormValues,
  getFormGroupState,
  getListControllerProps,
  getMutationMode,
  getNumberOrDefault,
  getQuery,
  getRecordForLocale,
  getRecordFromLocation,
  getResourceFieldLabelKey,
  getSelectedReferencesStatus,
  getSimpleValidationResolver,
  getStatusForArrayInput,
  getStatusForInput,
  getStorage,
  getSuggestionsFactory,
  getValuesFromRecords_default,
  hasCustomParams,
  inferTypeFromValues,
  injectedProps,
  isEmpty,
  isRequired,
  localStorageStore,
  maxLength,
  maxValue,
  memoryStore,
  mergeRefs,
  mergeTranslations,
  minLength,
  minValue,
  number,
  parseQueryFromLocation,
  queryReducer,
  reactAdminFetchActions,
  regex,
  removeDoubleSlashes,
  removeEmpty_default,
  removeKey_default,
  required,
  resolveBrowserLocale,
  sanitizeFetchType,
  sanitizeListRestProps,
  setSubmissionErrors,
  shallowEqual,
  substituteTokens,
  testDataProvider,
  testI18nProvider,
  undoableEventEmitter_default,
  useAddNotificationContext,
  useAddUndoableMutation,
  useApplyInputDefaultValues,
  useAugmentedForm,
  useAuthProvider_default,
  useAuthState_default,
  useAuthenticated,
  useBasename,
  useCanAccess,
  useCanAccessCallback,
  useCanAccessResources,
  useCheckAuth,
  useCheckForApplicationUpdate,
  useCheckMinimumRequiredProps,
  useChoices,
  useChoicesContext,
  useCloseNotification,
  useCreate,
  useCreateContext,
  useCreateController,
  useCreatePath,
  useDataProvider,
  useDataTableCallbacksContext,
  useDataTableColumnRankContext,
  useDataTableConfigContext,
  useDataTableDataContext,
  useDataTableRenderContext,
  useDataTableSelectedIdsContext,
  useDataTableSortContext,
  useDataTableStoreContext,
  useDebouncedEvent,
  useDeepCompareEffect,
  useDefaultTitle,
  useDelete,
  useDeleteMany,
  useDeleteWithConfirmController_default,
  useDeleteWithUndoController_default,
  useEditContext,
  useEditController,
  useEvent,
  useExpandAll,
  useExpanded,
  useFieldValue,
  useFilterState_default,
  useFirstResourceWithListAccess,
  useFormGroup,
  useFormGroupContext,
  useFormGroups,
  useGetIdentity,
  useGetList,
  useGetMany,
  useGetManyAggregate,
  useGetManyReference,
  useGetOne,
  useGetPathForRecord,
  useGetPathForRecordCallback,
  useGetPermissions_default,
  useGetRecordId,
  useGetRecordRepresentation,
  useGetResourceLabel,
  useGetValidationErrorMessage,
  useHandleAuthCallback,
  useHasDashboard,
  useI18nProvider,
  useInfiniteGetList,
  useInfiniteListController,
  useInfinitePaginationContext,
  useInput,
  useIsAuthPending,
  useIsDataLoaded,
  useIsMounted,
  useList,
  useListContext,
  useListContextWithProps,
  useListController,
  useListFilterContext,
  useListPaginationContext,
  useListParams,
  useListSortContext,
  useLoading,
  useLocale,
  useLocaleState,
  useLocales,
  useLogin_default,
  useLogoutIfAccessDenied_default,
  useLogout_default,
  useMutationMiddlewares,
  useNotificationContext,
  useNotify,
  useNotifyIsFormInvalid,
  useOptionalSourceContext,
  usePaginationState_default,
  usePermissions_default,
  usePickFilterContext,
  usePickPaginationContext,
  usePickSaveContext,
  usePickSortContext,
  usePreference,
  usePreferenceInput,
  usePreferenceKey,
  usePreferencesEditor,
  usePrevNextController,
  usePrevious,
  useRecordContext,
  useRecordFromLocation,
  useRecordSelection,
  useRedirect,
  useReference,
  useReferenceArrayFieldController,
  useReferenceArrayInputController,
  useReferenceFieldContext,
  useReferenceFieldController,
  useReferenceInputController,
  useReferenceManyFieldController,
  useReferenceOneFieldController,
  useRefresh,
  useRegisterMutationMiddleware,
  useRemoveFromStore,
  useRemoveItemsFromStore,
  useRequireAccess,
  useResetErrorBoundaryOnLocationChange,
  useResetStore,
  useResourceContext,
  useResourceDefinition,
  useResourceDefinitionContext,
  useResourceDefinitions,
  useRestoreScrollPosition,
  useSafeSetState,
  useSaveContext,
  useScrollToTop,
  useSelectAll,
  useSetInspectorTitle,
  useSetLocale,
  useShowContext,
  useShowController,
  useSortState_default,
  useSourceContext,
  useSplatPathBase,
  useStore,
  useStoreContext,
  useSuggestions,
  useTakeUndoableMutation,
  useTimeout,
  useTrackScrollPosition,
  useTranslatable,
  useTranslatableContext,
  useTranslate,
  useTranslateLabel,
  useUnique,
  useUnselect,
  useUnselectAll,
  useUpdate,
  useUpdateMany,
  useWarnWhenUnsavedChanges,
  useWhyDidYouUpdate,
  useWrappedSource,
  warning_default,
  withLifecycleCallbacks
} from "./chunk-NU6QNSKG.js";
import "./chunk-6QRWIXFX.js";
import "./chunk-6BY6K3QO.js";
import "./chunk-6YAT23RL.js";
import {
  esm_default
} from "./chunk-CNSGX4I7.js";
import {
  esm_default as esm_default2
} from "./chunk-JB4G3OFW.js";
import "./chunk-OKOUEKDN.js";
import "./chunk-QJPESCYV.js";
import "./chunk-JMO76HME.js";
import "./chunk-IHIFIR7R.js";
import "./chunk-MUMTMDIN.js";
import "./chunk-U4WOVAWO.js";
import "./chunk-S5QVHTMZ.js";
import "./chunk-TKQHW6KT.js";
import "./chunk-XYBWNCZY.js";
import "./chunk-C6WWHQR7.js";
import "./chunk-BI67QTZH.js";
import "./chunk-7MZ2WSTP.js";
import "./chunk-FHFFJGS3.js";
import "./chunk-FH2CGZEX.js";
import "./chunk-ICXJ7EWG.js";
import "./chunk-U7P2NEEE.js";
import "./chunk-AYOM2JT2.js";
import "./chunk-DVRKUS6M.js";
import "./chunk-NAXKE64U.js";
import {
  require_react
} from "./chunk-HPJJ3TUJ.js";
import {
  __toESM
} from "./chunk-SNAQBZPT.js";

// node_modules/react-admin/dist/esm/Admin.js
var React = __toESM(require_react());

// node_modules/react-admin/dist/esm/defaultI18nProvider.js
var defaultI18nProvider = esm_default(function() {
  return esm_default2;
}, "en", [{ name: "en", value: "English" }], { allowMissing: true });

// node_modules/react-admin/dist/esm/Admin.js
var defaultStore = localStorageStore();
var Admin = function(props) {
  var accessDenied = props.accessDenied, authCallbackPage = props.authCallbackPage, authenticationError = props.authenticationError, authProvider = props.authProvider, basename = props.basename, catchAll = props.catchAll, children = props.children, darkTheme = props.darkTheme, dashboard = props.dashboard, dataProvider = props.dataProvider, defaultTheme2 = props.defaultTheme, disableTelemetry = props.disableTelemetry, error = props.error, _a = props.i18nProvider, i18nProvider = _a === void 0 ? defaultI18nProvider : _a, layout = props.layout, lightTheme = props.lightTheme, loading = props.loading, loginPage = props.loginPage, notification = props.notification, queryClient = props.queryClient, ready = props.ready, requireAuth = props.requireAuth, _b = props.store, store = _b === void 0 ? defaultStore : _b, theme = props.theme, _c = props.title, title = _c === void 0 ? "React Admin" : _c;
  if (loginPage === true && true) {
    console.warn("You passed true to the loginPage prop. You must either pass false to disable it or a component class to customize it");
  }
  return React.createElement(
    AdminContext,
    { authProvider, basename, darkTheme, dataProvider, defaultTheme: defaultTheme2, i18nProvider, lightTheme, queryClient, store, theme },
    React.createElement(AdminUI, { accessDenied, authCallbackPage, authenticationError, catchAll, dashboard, disableTelemetry, error, layout, loading, loginPage, notification, ready, requireAuth, title }, children)
  );
};
export {
  AUTH_CHECK,
  AUTH_ERROR,
  AUTH_GET_PERMISSIONS,
  AUTH_LOGIN,
  AUTH_LOGOUT,
  AccessDenied,
  AccessDeniedClasses,
  AddItemButton,
  AddNotificationContext,
  AddSavedQueryDialog,
  AddSavedQueryIconButton,
  AddUndoableMutationContext,
  Admin,
  AdminContext,
  AdminRouter,
  AdminUI,
  AppBar,
  AppBarClasses,
  ApplicationUpdatedNotification,
  ArrayField,
  ArrayInput,
  ArrayInputClasses,
  ArrayInputContext,
  AuthCallback,
  AuthContext,
  AuthError,
  AuthErrorClasses,
  AuthLayout,
  AuthLayoutClasses,
  Authenticated,
  AuthenticationError,
  AuthenticationErrorClasses,
  AutocompleteArrayInput,
  AutocompleteInput,
  AutocompleteInputClasses,
  BasenameContextProvider,
  BooleanField,
  BooleanInput,
  BulkActionsToolbar,
  BulkActionsToolbarClasses,
  BulkDeleteButton,
  BulkDeleteWithConfirmButton,
  BulkDeleteWithUndoButton,
  BulkExportButton,
  BulkUpdateButton,
  BulkUpdateWithConfirmButton,
  BulkUpdateWithUndoButton,
  Button,
  CLOSED_DRAWER_WIDTH,
  CREATE,
  CanAccess,
  CardContentInner,
  CardContentInnerClasses,
  CheckForApplicationUpdate,
  CheckboxGroupInput,
  CheckboxGroupInputClasses,
  ChipField,
  ChoicesContext,
  ChoicesContextProvider,
  CloneButton_default as CloneButton,
  CloseNotificationContext,
  ColumnsButton,
  ColumnsSelector,
  ColumnsSelectorItem,
  Configurable,
  ConfigurableClasses,
  Confirm,
  ConfirmClasses,
  CoreAdmin,
  CoreAdminContext,
  CoreAdminRoutes,
  CoreAdminUI,
  Count,
  Create,
  CreateActions,
  CreateBase,
  CreateButton_default as CreateButton,
  CreateButtonClasses,
  CreateClasses,
  CreateContext,
  CreateContextProvider,
  CreateController,
  CreateView,
  CustomRoutes,
  DEFAULT_LOCALE,
  DELETE,
  DELETE_MANY,
  DRAWER_WIDTH,
  DashboardMenuItem,
  DataProviderContext_default as DataProviderContext,
  DataTable,
  DataTableBase,
  DataTableBody,
  DataTableCallbacksContext,
  DataTableCell,
  DataTableClasses,
  DataTableColumn,
  DataTableColumnRankContext,
  DataTableConfigContext,
  DataTableDataContext,
  DataTableHead,
  DataTableHeadCell,
  DataTableLoading,
  DataTableNumberColumn,
  DataTableRenderContext,
  DataTableRoot,
  DataTableRow,
  DataTableRowSxContext,
  DataTableSelectedIdsContext,
  DataTableSortContext,
  DataTableStoreContext,
  Datagrid,
  DatagridBody_default as DatagridBody,
  DatagridCell_default as DatagridCell,
  DatagridClasses,
  DatagridConfigurable,
  DatagridHeader,
  DatagridHeaderCell_default as DatagridHeaderCell,
  DatagridHeaderCellClasses,
  DatagridInput,
  DatagridLoading_default as DatagridLoading,
  DatagridPrefix,
  DatagridRoot,
  DatagridRow_default as DatagridRow,
  DateField,
  DateInput,
  DateTimeInput,
  DefaultTitleContext,
  DefaultTitleContextProvider,
  DeleteButton,
  DeleteWithConfirmButton,
  DeleteWithUndoButton,
  DeviceTestWrapper,
  Edit,
  EditActions,
  EditBase,
  EditButton,
  EditButtonClasses,
  EditClasses,
  EditContext,
  EditContextProvider,
  EditController,
  EditGuesser,
  EditView,
  EmailField,
  Empty,
  EmptyClasses,
  Error,
  ErrorClasses,
  ExpandRowButton_default as ExpandRowButton,
  ExportButton,
  ExporterContext,
  FieldTitle_default as FieldTitle,
  FieldToggle,
  FieldsSelector,
  FileField,
  FileInput,
  FileInputClasses,
  Filter,
  FilterButton,
  FilterButtonMenuItem,
  FilterClasses,
  FilterContext,
  FilterForm,
  FilterFormBase,
  FilterFormClasses,
  FilterFormInput,
  FilterFormInputClasses,
  FilterList,
  FilterListItem,
  FilterListItemClasses,
  FilterListSection,
  FilterLiveForm,
  FilterLiveSearch,
  Form,
  FormDataConsumer,
  FormDataConsumerView,
  FormGroupContext,
  FormGroupContextProvider,
  FormGroupsProvider,
  FormTab,
  FormTabHeader,
  FunctionField,
  GET_LIST,
  GET_MANY,
  GET_MANY_REFERENCE,
  GET_ONE,
  HIDE_FILTER,
  HasDashboardContext,
  HasDashboardContextProvider,
  HideOnScroll,
  HttpError_default as HttpError,
  I18N_CHANGE_LOCALE,
  I18N_TRANSLATE,
  I18nContext,
  I18nContextProvider,
  IconButtonWithTooltip,
  ImageField,
  ImageFieldClasses,
  ImageInput,
  InPlaceEditor,
  InferenceTypes,
  InferredElement_default as InferredElement,
  InfiniteList,
  InfiniteListBase,
  InfinitePagination,
  InfinitePaginationContext,
  InputHelperText,
  Inspector,
  InspectorButton,
  InspectorClasses,
  InspectorRoot,
  Labeled,
  LabeledClasses,
  Layout,
  LayoutClasses,
  LinearProgress,
  Link,
  LinkClasses,
  List,
  ListActions,
  ListBase,
  ListButton,
  ListClasses,
  ListContext,
  ListContextProvider,
  ListController,
  ListFilterContext,
  ListGuesser,
  ListNoResults,
  ListPaginationContext,
  ListSortContext,
  ListToolbar,
  ListView,
  Loading,
  LoadingClasses,
  LoadingIndicator,
  LoadingIndicatorClasses,
  LoadingInput,
  LoadingPage,
  LocalesMenuButton,
  LocalesMenuButtonClasses,
  Login,
  LoginClasses,
  LoginForm,
  LoginFormClasses,
  LoginWithEmail,
  Logout,
  LogoutClasses,
  LogoutOnMount,
  Menu,
  MenuClasses,
  MenuItemLink,
  MenuItemLinkClasses,
  NavigateToFirstResource,
  NotFound,
  NotFoundClasses,
  Notification,
  NotificationClasses,
  NotificationContext,
  NotificationContextProvider,
  NullableBooleanInput,
  NullableBooleanInputClasses,
  NumberField,
  NumberInput,
  OptionalRecordContextProvider,
  OptionalResourceContextProvider,
  PageTitleConfigurable,
  PageTitleEditor,
  Pagination,
  PaginationActions,
  PasswordInput,
  Placeholder,
  PreferenceKeyContext,
  PreferenceKeyContextProvider,
  PreferencesEditorContext,
  PreferencesEditorContextProvider,
  PrevNextButtonClasses,
  PrevNextButtons,
  PreviousLocationStorageKey,
  PureDatagridBody,
  PureDatagridRow,
  RadioButtonGroupInput,
  RadioButtonGroupInputClasses,
  ReOrderButtons,
  Ready_default as Ready,
  RecordContext,
  RecordContextProvider,
  RecordRepresentation,
  ReferenceArrayField,
  ReferenceArrayFieldClasses,
  ReferenceArrayFieldView,
  ReferenceArrayInput,
  ReferenceError,
  ReferenceField,
  ReferenceFieldBase,
  ReferenceFieldClasses,
  ReferenceFieldContext,
  ReferenceFieldContextProvider,
  ReferenceFieldView,
  ReferenceInput,
  ReferenceInputBase,
  ReferenceManyCount,
  ReferenceManyField,
  ReferenceOneField,
  RefreshButton,
  RefreshIconButton,
  RemoveItemButton,
  RemoveSavedQueryDialog,
  RemoveSavedQueryIconButton,
  ResettableTextField,
  ResettableTextFieldClasses,
  ResettableTextFieldStyles,
  Resource,
  ResourceContext,
  ResourceContextProvider,
  ResourceDefinitionContext,
  ResourceDefinitionContextProvider,
  ResourceMenuItem,
  ResourceMenuItems,
  RestoreScrollPosition,
  RichTextField,
  SET_FILTER,
  SET_PAGE,
  SET_PER_PAGE,
  SET_SORT,
  SHOW_FILTER,
  SORT_ASC,
  SORT_DESC,
  SaveButton,
  SaveContext,
  SaveContextProvider,
  SavedQueriesList,
  SavedQueriesListClasses,
  SavedQueryFilterListItem,
  SavedQueryFilterListItemClasses,
  SearchInput,
  SelectAllButton,
  SelectArrayInput,
  SelectArrayInputClasses,
  SelectColumnsButton,
  SelectField,
  SelectInput,
  SelectPageCheckbox,
  SelectRowCheckbox,
  Show,
  ShowActions,
  ShowBase,
  ShowButton_default as ShowButton,
  ShowClasses,
  ShowContext,
  ShowContextProvider,
  ShowController,
  ShowGuesser,
  ShowView,
  Sidebar,
  SidebarClasses,
  SidebarToggleButton,
  SidebarToggleButtonClasses,
  SimpleForm,
  SimpleFormConfigurable,
  SimpleFormIterator,
  SimpleFormIteratorClasses,
  SimpleFormIteratorContext,
  SimpleFormIteratorItem,
  SimpleFormIteratorItemContext,
  SimpleFormIteratorPrefix,
  SimpleList,
  SimpleListClasses,
  SimpleListConfigurable,
  SimpleListLoading,
  SimpleListLoadingClasses,
  SimpleShowLayout,
  SimpleShowLayoutClasses,
  SingleFieldList,
  SingleFieldListClasses,
  SkipNavigationButton,
  SortButton_default as SortButton,
  SourceContext,
  SourceContextProvider,
  StoreContext,
  StoreContextProvider,
  StoreSetter,
  Tab,
  TabClasses,
  TabbedForm,
  TabbedFormClasses,
  TabbedFormTabs,
  TabbedFormView,
  TabbedShowLayout,
  TabbedShowLayoutClasses,
  TabbedShowLayoutTabs,
  TakeUndoableMutationContext,
  TestMemoryRouter,
  TestTranslationProvider,
  TextArrayInput,
  TextField,
  TextInput,
  ThemeProvider,
  ThemesContext,
  TimeInput,
  Title,
  TitlePortal,
  ToggleThemeButton,
  Toolbar,
  ToolbarClasses,
  TopToolbar,
  TranslatableContext,
  TranslatableContextProvider,
  TranslatableFields,
  TranslatableFieldsTab,
  TranslatableFieldsTabContent,
  TranslatableInputs,
  TranslatableInputsClasses,
  TranslatableInputsTab,
  TranslatableInputsTabClasses,
  TranslatableInputsTabContent,
  TranslatableInputsTabContentClasses,
  TranslatableInputsTabs,
  TranslatableInputsTabsClasses,
  Translate,
  UPDATE,
  UPDATE_MANY,
  UndoableMutationsContextProvider,
  UpdateButton,
  UpdateWithConfirmButton,
  UpdateWithUndoButton,
  UrlField,
  UserMenu,
  UserMenuClasses,
  UserMenuContext,
  ValidationError,
  WarnWhenUnsavedChanges,
  WithListContext,
  WithPermissions_default as WithPermissions,
  WithRecord,
  WrapperField,
  addRefreshAuthToAuthProvider,
  addRefreshAuthToDataProvider,
  applyCallbacks,
  areValidSavedQueries,
  asyncDebounce,
  bwDarkTheme,
  bwLightTheme,
  choices,
  combine2Validators,
  combineDataProviders,
  composeSyncValidators,
  composeValidators,
  convertLegacyAuthProvider_default as convertLegacyAuthProvider,
  convertLegacyDataProvider_default as convertLegacyDataProvider,
  defaultDarkTheme,
  defaultDataProvider,
  defaultExporter,
  defaultI18nProvider,
  defaultLightTheme,
  defaultTheme,
  downloadCSV,
  editFieldTypes,
  email,
  escapePath_default as escapePath,
  extractValidSavedQueries,
  fetchActionsWithArrayOfIdentifiedRecordsResponse,
  fetchActionsWithArrayOfRecordsResponse,
  fetchActionsWithRecordResponse,
  fetchActionsWithTotalResponse,
  fetchRelatedRecords,
  fetch_exports as fetchUtils,
  findTabsWithErrors,
  getArrayInputError,
  getElementsFromRecords_default as getElementsFromRecords,
  getFieldLabelTranslationArgs,
  getFilterFormValues,
  getFormGroupState,
  getListControllerProps,
  getMutationMode,
  getNumberOrDefault,
  getQuery,
  getRecordForLocale,
  getRecordFromLocation,
  getResourceFieldLabelKey,
  getSelectedReferencesStatus,
  getShowLayoutTabFullPath,
  getSimpleValidationResolver,
  getStatusForArrayInput,
  getStatusForInput,
  getStorage,
  getSuggestionsFactory,
  getTabbedFormTabFullPath,
  getValuesFromRecords_default as getValuesFromRecords,
  hasCustomParams,
  houseDarkTheme,
  houseLightTheme,
  inferTypeFromValues,
  injectedProps,
  isEmpty,
  isRequired,
  isValidSavedQuery,
  listFieldTypes,
  localStorageStore,
  maxLength,
  maxValue,
  memoryStore,
  mergeRefs,
  mergeTranslations,
  minLength,
  minValue,
  nanoDarkTheme,
  nanoLightTheme,
  number,
  parseQueryFromLocation,
  queryReducer,
  radiantDarkTheme,
  radiantLightTheme,
  reactAdminFetchActions,
  regex,
  removeDoubleSlashes,
  removeEmpty_default as removeEmpty,
  removeKey_default as removeKey,
  removeTags,
  required,
  resolveBrowserLocale,
  sanitizeFetchType,
  sanitizeFieldRestProps,
  sanitizeInputRestProps,
  sanitizeListRestProps,
  setSubmissionErrors,
  shallowEqual,
  showFieldTypes,
  substituteTokens,
  testDataProvider,
  testI18nProvider,
  undoableEventEmitter_default as undoableEventEmitter,
  useAddNotificationContext,
  useAddUndoableMutation,
  useApplyInputDefaultValues,
  useArrayInput,
  useAugmentedForm,
  useAuthProvider_default as useAuthProvider,
  useAuthState_default as useAuthState,
  useAuthenticated,
  useBasename,
  useCanAccess,
  useCanAccessCallback,
  useCanAccessResources,
  useCheckAuth,
  useCheckForApplicationUpdate,
  useCheckMinimumRequiredProps,
  useChoices,
  useChoicesContext,
  useCloseNotification,
  useCreate,
  useCreateContext,
  useCreateController,
  useCreatePath,
  useCreateSuggestionContext,
  useDataProvider,
  useDataTableCallbacksContext,
  useDataTableColumnRankContext,
  useDataTableConfigContext,
  useDataTableDataContext,
  useDataTableRenderContext,
  useDataTableRowSxContext,
  useDataTableSelectedIdsContext,
  useDataTableSortContext,
  useDataTableStoreContext,
  useDatagridContext,
  useDebouncedEvent,
  useDeepCompareEffect,
  useDefaultTitle,
  useDelete,
  useDeleteMany,
  useDeleteWithConfirmController_default as useDeleteWithConfirmController,
  useDeleteWithUndoController_default as useDeleteWithUndoController,
  useEditContext,
  useEditController,
  useEvent,
  useExpandAll,
  useExpanded,
  useFieldValue,
  useFilterState_default as useFilterState,
  useFirstResourceWithListAccess,
  useFormGroup,
  useFormGroupContext,
  useFormGroups,
  useGetIdentity,
  useGetList,
  useGetMany,
  useGetManyAggregate,
  useGetManyReference,
  useGetOne,
  useGetPathForRecord,
  useGetPathForRecordCallback,
  useGetPermissions_default as useGetPermissions,
  useGetRecordId,
  useGetRecordRepresentation,
  useGetResourceLabel,
  useGetValidationErrorMessage,
  useHandleAuthCallback,
  useHasDashboard,
  useI18nProvider,
  useInfiniteGetList,
  useInfiniteListController,
  useInfinitePaginationContext,
  useInput,
  useIsAuthPending,
  useIsDataLoaded,
  useIsMounted,
  useList,
  useListContext,
  useListContextWithProps,
  useListController,
  useListFilterContext,
  useListPaginationContext,
  useListParams,
  useListSortContext,
  useLoading,
  useLocale,
  useLocaleState,
  useLocales,
  useLogin_default as useLogin,
  useLogout_default as useLogout,
  useLogoutIfAccessDenied_default as useLogoutIfAccessDenied,
  useMutationMiddlewares,
  useNotificationContext,
  useNotify,
  useNotifyIsFormInvalid,
  useOptionalSourceContext,
  usePaginationState_default as usePaginationState,
  usePermissions_default as usePermissions,
  usePickFilterContext,
  usePickPaginationContext,
  usePickSaveContext,
  usePickSortContext,
  usePreference,
  usePreferenceInput,
  usePreferenceKey,
  usePreferencesEditor,
  usePrevNextController,
  usePrevious,
  useRecordContext,
  useRecordFromLocation,
  useRecordSelection,
  useRedirect,
  useReference,
  useReferenceArrayFieldController,
  useReferenceArrayInputController,
  useReferenceFieldContext,
  useReferenceFieldController,
  useReferenceInputController,
  useReferenceManyFieldController,
  useReferenceOneFieldController,
  useRefresh,
  useRegisterMutationMiddleware,
  useRemoveFromStore,
  useRemoveItemsFromStore,
  useRequireAccess,
  useResetErrorBoundaryOnLocationChange,
  useResetStore,
  useResourceContext,
  useResourceDefinition,
  useResourceDefinitionContext,
  useResourceDefinitions,
  useRestoreScrollPosition,
  useSafeSetState,
  useSaveContext,
  useSavedQueries,
  useScrollToTop,
  useSelectAll,
  useSetInspectorTitle,
  useSetLocale,
  useShowContext,
  useShowController,
  useSidebarState,
  useSimpleFormIterator,
  useSimpleFormIteratorItem,
  useSortState_default as useSortState,
  useSourceContext,
  useSplatPathBase,
  useStore,
  useStoreContext,
  useSuggestions,
  useSupportCreateSuggestion,
  useTakeUndoableMutation,
  useTheme,
  useThemesContext,
  useTimeout,
  useTrackScrollPosition,
  useTranslatable,
  useTranslatableContext,
  useTranslate,
  useTranslateLabel,
  useUnique,
  useUnselect,
  useUnselectAll,
  useUpdate,
  useUpdateMany,
  useUserMenu,
  useWarnWhenUnsavedChanges,
  useWhyDidYouUpdate,
  useWrappedSource,
  warning_default as warning,
  withLifecycleCallbacks
};
//# sourceMappingURL=react-admin.js.map

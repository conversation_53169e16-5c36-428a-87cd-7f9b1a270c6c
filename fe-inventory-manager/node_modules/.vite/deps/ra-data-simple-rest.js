import {
  fetch_exports
} from "./chunk-NU6QNSKG.js";
import "./chunk-6QRWIXFX.js";
import "./chunk-6BY6K3QO.js";
import {
  require_query_string
} from "./chunk-6YAT23RL.js";
import "./chunk-QJPESCYV.js";
import "./chunk-FH2CGZEX.js";
import "./chunk-NAXKE64U.js";
import "./chunk-HPJJ3TUJ.js";
import {
  __toESM
} from "./chunk-SNAQBZPT.js";

// node_modules/ra-data-simple-rest/dist/esm/index.js
var import_query_string = __toESM(require_query_string());
var __assign = function() {
  __assign = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
var esm_default = function(apiUrl, httpClient, countHeader) {
  if (httpClient === void 0) {
    httpClient = fetch_exports.fetchJson;
  }
  if (countHeader === void 0) {
    countHeader = "Content-Range";
  }
  return {
    getList: function(resource, params) {
      var _a = params.pagination || { page: 1, perPage: 10 }, page = _a.page, perPage = _a.perPage;
      var _b = params.sort || { field: "id", order: "ASC" }, field = _b.field, order = _b.order;
      var rangeStart = (page - 1) * perPage;
      var rangeEnd = page * perPage - 1;
      var query = {
        sort: JSON.stringify([field, order]),
        range: JSON.stringify([rangeStart, rangeEnd]),
        filter: JSON.stringify(params.filter)
      };
      var url = "".concat(apiUrl, "/").concat(resource, "?").concat((0, import_query_string.stringify)(query));
      var options = countHeader === "Content-Range" ? {
        // Chrome doesn't return `Content-Range` header if no `Range` is provided in the request.
        headers: new Headers({
          Range: "".concat(resource, "=").concat(rangeStart, "-").concat(rangeEnd)
        }),
        signal: params === null || params === void 0 ? void 0 : params.signal
      } : { signal: params === null || params === void 0 ? void 0 : params.signal };
      return httpClient(url, options).then(function(_a2) {
        var headers = _a2.headers, json = _a2.json;
        if (!headers.has(countHeader)) {
          throw new Error("The ".concat(countHeader, " header is missing in the HTTP Response. The simple REST data provider expects responses for lists of resources to contain this header with the total number of results to build the pagination. If you are using CORS, did you declare ").concat(countHeader, " in the Access-Control-Expose-Headers header?"));
        }
        return {
          data: json,
          total: countHeader === "Content-Range" ? parseInt(headers.get("content-range").split("/").pop() || "", 10) : parseInt(headers.get(countHeader.toLowerCase()))
        };
      });
    },
    getOne: function(resource, params) {
      return httpClient("".concat(apiUrl, "/").concat(resource, "/").concat(encodeURIComponent(params.id)), {
        signal: params === null || params === void 0 ? void 0 : params.signal
      }).then(function(_a) {
        var json = _a.json;
        return {
          data: json
        };
      });
    },
    getMany: function(resource, params) {
      var query = {
        filter: JSON.stringify({ id: params.ids })
      };
      var url = "".concat(apiUrl, "/").concat(resource, "?").concat((0, import_query_string.stringify)(query));
      return httpClient(url, { signal: params === null || params === void 0 ? void 0 : params.signal }).then(function(_a) {
        var json = _a.json;
        return {
          data: json
        };
      });
    },
    getManyReference: function(resource, params) {
      var _a;
      var _b = params.pagination, page = _b.page, perPage = _b.perPage;
      var _c = params.sort, field = _c.field, order = _c.order;
      var rangeStart = (page - 1) * perPage;
      var rangeEnd = page * perPage - 1;
      var query = {
        sort: JSON.stringify([field, order]),
        range: JSON.stringify([(page - 1) * perPage, page * perPage - 1]),
        filter: JSON.stringify(__assign(__assign({}, params.filter), (_a = {}, _a[params.target] = params.id, _a)))
      };
      var url = "".concat(apiUrl, "/").concat(resource, "?").concat((0, import_query_string.stringify)(query));
      var options = countHeader === "Content-Range" ? {
        // Chrome doesn't return `Content-Range` header if no `Range` is provided in the request.
        headers: new Headers({
          Range: "".concat(resource, "=").concat(rangeStart, "-").concat(rangeEnd)
        }),
        signal: params === null || params === void 0 ? void 0 : params.signal
      } : { signal: params === null || params === void 0 ? void 0 : params.signal };
      return httpClient(url, options).then(function(_a2) {
        var headers = _a2.headers, json = _a2.json;
        if (!headers.has(countHeader)) {
          throw new Error("The ".concat(countHeader, " header is missing in the HTTP Response. The simple REST data provider expects responses for lists of resources to contain this header with the total number of results to build the pagination. If you are using CORS, did you declare ").concat(countHeader, " in the Access-Control-Expose-Headers header?"));
        }
        return {
          data: json,
          total: countHeader === "Content-Range" ? parseInt(headers.get("content-range").split("/").pop() || "", 10) : parseInt(headers.get(countHeader.toLowerCase()))
        };
      });
    },
    update: function(resource, params) {
      return httpClient("".concat(apiUrl, "/").concat(resource, "/").concat(encodeURIComponent(params.id)), {
        method: "PUT",
        body: JSON.stringify(params.data)
      }).then(function(_a) {
        var json = _a.json;
        return { data: json };
      });
    },
    // simple-rest doesn't handle provide an updateMany route, so we fallback to calling update n times instead
    updateMany: function(resource, params) {
      return Promise.all(params.ids.map(function(id) {
        return httpClient("".concat(apiUrl, "/").concat(resource, "/").concat(encodeURIComponent(id)), {
          method: "PUT",
          body: JSON.stringify(params.data)
        });
      })).then(function(responses) {
        return {
          data: responses.map(function(_a) {
            var json = _a.json;
            return json.id;
          })
        };
      });
    },
    create: function(resource, params) {
      return httpClient("".concat(apiUrl, "/").concat(resource), {
        method: "POST",
        body: JSON.stringify(params.data)
      }).then(function(_a) {
        var json = _a.json;
        return { data: json };
      });
    },
    delete: function(resource, params) {
      return httpClient("".concat(apiUrl, "/").concat(resource, "/").concat(encodeURIComponent(params.id)), {
        method: "DELETE",
        headers: new Headers({
          "Content-Type": "text/plain"
        })
      }).then(function(_a) {
        var json = _a.json;
        return { data: json };
      });
    },
    // simple-rest doesn't handle filters on DELETE route, so we fallback to calling DELETE n times instead
    deleteMany: function(resource, params) {
      return Promise.all(params.ids.map(function(id) {
        return httpClient("".concat(apiUrl, "/").concat(resource, "/").concat(encodeURIComponent(id)), {
          method: "DELETE",
          headers: new Headers({
            "Content-Type": "text/plain"
          })
        });
      })).then(function(responses) {
        return {
          data: responses.map(function(_a) {
            var json = _a.json;
            return json.id;
          })
        };
      });
    }
  };
};
export {
  esm_default as default
};
//# sourceMappingURL=ra-data-simple-rest.js.map

{"version": 3, "sources": ["../../react-admin/src/Admin.tsx", "../../react-admin/src/defaultI18nProvider.ts"], "sourcesContent": ["import * as React from 'react';\nimport { localStorageStore } from 'ra-core';\nimport {\n    AdminUI,\n    AdminContext,\n    AdminContextProps,\n    AdminUIProps,\n} from 'ra-ui-materialui';\n\nimport { defaultI18nProvider } from './defaultI18nProvider';\nconst defaultStore = localStorageStore();\n\n/**\n * Main admin component, entry point to the application.\n *\n * Initializes the various contexts (auth, data, i18n, router)\n * and defines the main routes.\n *\n * Expects a list of resources as children, or a function returning a list of\n * resources based on the permissions.\n *\n * @example\n *\n * // static list of resources\n *\n * import {\n *     Admin,\n *     Resource,\n *     ListGuesser,\n *     useDataProvider,\n * } from 'react-admin';\n *\n * const App = () => (\n *     <Admin dataProvider={myDataProvider}>\n *         <Resource name=\"posts\" list={ListGuesser} />\n *     </Admin>\n * );\n *\n * // dynamic list of resources based on permissions\n *\n * import {\n *     Admin,\n *     Resource,\n *     ListGuesser,\n *     useDataProvider,\n * } from 'react-admin';\n *\n * const App = () => (\n *     <Admin dataProvider={myDataProvider}>\n *         {permissions => [\n *             <Resource name=\"posts\" key=\"posts\" list={ListGuesser} />,\n *         ]}\n *     </Admin>\n * );\n *\n * // If you have to build a dynamic list of resources using a side effect,\n * // you can't use <Admin>. But as it delegates to sub components,\n * // it's relatively straightforward to replace it:\n *\n * import * as React from 'react';\nimport { useEffect, useState } from 'react';\n * import {\n *     AdminContext,\n *     AdminUI,\n *     defaultI18nProvider,\n *     localStorageStore,\n *     Resource,\n *     ListGuesser,\n *     useDataProvider,\n * } from 'react-admin';\n *\n * const store = localStorageStore();\n *\n * const App = () => (\n *     <AdminContext dataProvider={myDataProvider} i18nProvider={defaultI18nProvider} store={store}>\n *         <Resources />\n *     </AdminContext>\n * );\n *\n * const Resources = () => {\n *     const [resources, setResources] = useState([]);\n *     const dataProvider = useDataProvider();\n *     useEffect(() => {\n *         dataProvider.introspect().then(r => setResources(r));\n *     }, []);\n *\n *     return (\n *         <AdminUI>\n *             {resources.map(resource => (\n *                 <Resource name={resource.name} key={resource.key} list={ListGuesser} />\n *             ))}\n *         </AdminUI>\n *     );\n * };\n */\nexport const Admin = (props: AdminProps) => {\n    const {\n        accessDenied,\n        authCallbackPage,\n        authenticationError,\n        authProvider,\n        basename,\n        catchAll,\n        children,\n        darkTheme,\n        dashboard,\n        dataProvider,\n        defaultTheme,\n        disableTelemetry,\n        error,\n        i18nProvider = defaultI18nProvider,\n        layout,\n        lightTheme,\n        loading,\n        loginPage,\n        notification,\n        queryClient,\n        ready,\n        requireAuth,\n        store = defaultStore,\n        theme,\n        title = 'React Admin',\n    } = props;\n\n    if (loginPage === true && process.env.NODE_ENV !== 'production') {\n        console.warn(\n            'You passed true to the loginPage prop. You must either pass false to disable it or a component class to customize it'\n        );\n    }\n\n    return (\n        <AdminContext\n            authProvider={authProvider}\n            basename={basename}\n            darkTheme={darkTheme}\n            dataProvider={dataProvider}\n            defaultTheme={defaultTheme}\n            i18nProvider={i18nProvider}\n            lightTheme={lightTheme}\n            queryClient={queryClient}\n            store={store}\n            theme={theme}\n        >\n            <AdminUI\n                accessDenied={accessDenied}\n                authCallbackPage={authCallbackPage}\n                authenticationError={authenticationError}\n                catchAll={catchAll}\n                dashboard={dashboard}\n                disableTelemetry={disableTelemetry}\n                error={error}\n                layout={layout}\n                loading={loading}\n                loginPage={loginPage}\n                notification={notification}\n                ready={ready}\n                requireAuth={requireAuth}\n                title={title}\n            >\n                {children}\n            </AdminUI>\n        </AdminContext>\n    );\n};\n\nexport default Admin;\n\nexport interface AdminProps extends AdminContextProps, AdminUIProps {}\n", "import defaultMessages from 'ra-language-english';\nimport polyglotI18nProvider from 'ra-i18n-polyglot';\n\nexport const defaultI18nProvider = polyglotI18nProvider(\n    () => defaultMessages,\n    'en',\n    [{ name: 'en', value: 'English' }],\n    { allowMissing: true }\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;;;ACGhB,IAAM,sBAAsB,YAC/B,WAAA;AAAM,SAAAA;AAAA,GACN,MACA,CAAC,EAAE,MAAM,MAAM,OAAO,UAAS,CAAE,GACjC,EAAE,cAAc,KAAI,CAAE;;;ADG1B,IAAM,eAAe,kBAAiB;AAqF/B,IAAM,QAAQ,SAAC,OAAiB;AAE/B,MAAA,eAyBA,MAAK,cAxBL,mBAwBA,MAAK,kBAvBL,sBAuBA,MAAK,qBAtBL,eAsBA,MAAK,cArBL,WAqBA,MAAK,UApBL,WAoBA,MAAK,UAnBL,WAmBA,MAAK,UAlBL,YAkBA,MAAK,WAjBL,YAiBA,MAAK,WAhBL,eAgBA,MAAK,cAfLC,gBAeA,MAAK,cAdL,mBAcA,MAAK,kBAbL,QAaA,MAAK,OAZL,KAYA,MAAK,cAZL,eAAY,OAAA,SAAG,sBAAmB,IAClC,SAWA,MAAK,QAVL,aAUA,MAAK,YATL,UASA,MAAK,SARL,YAQA,MAAK,WAPL,eAOA,MAAK,cANL,cAMA,MAAK,aALL,QAKA,MAAK,OAJL,cAIA,MAAK,aAHL,KAGA,MAAK,OAHL,QAAK,OAAA,SAAG,eAAY,IACpB,QAEA,MAAK,OADL,KACA,MAAK,OADL,QAAK,OAAA,SAAG,gBAAa;AAGzB,MAAI,cAAc,QAAQ,MAAuC;AAC7D,YAAQ,KACJ,sHAAsH;;AAI9H,SACI;IAAC;IAAY,EACT,cACA,UACA,WACA,cACA,cAAcA,eACd,cACA,YACA,aACA,OACA,MAAY;IAEZ,oBAAC,SAAO,EACJ,cACA,kBACA,qBACA,UACA,WACA,kBACA,OACA,QACA,SACA,WACA,cACA,OACA,aACA,MAAY,GAEX,QAAQ;EACH;AAGtB;", "names": ["esm_default", "defaultTheme"]}
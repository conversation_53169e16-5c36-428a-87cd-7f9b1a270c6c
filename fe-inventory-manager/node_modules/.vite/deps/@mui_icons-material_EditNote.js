"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-BI67QTZH.js";
import "./chunk-7MZ2WSTP.js";
import "./chunk-FHFFJGS3.js";
import "./chunk-FH2CGZEX.js";
import "./chunk-ICXJ7EWG.js";
import "./chunk-U7P2NEEE.js";
import "./chunk-AYOM2JT2.js";
import "./chunk-DVRKUS6M.js";
import {
  require_jsx_runtime
} from "./chunk-NAXKE64U.js";
import "./chunk-HPJJ3TUJ.js";
import {
  __toESM
} from "./chunk-SNAQBZPT.js";

// node_modules/@mui/icons-material/esm/EditNote.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var EditNote_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M3 10h11v2H3zm0-2h11V6H3zm0 8h7v-2H3zm15.01-3.13.71-.71c.39-.39 1.02-.39 1.41 0l.71.71c.39.39.39 1.02 0 1.41l-.71.71zm-.71.71-5.3 5.3V21h2.12l5.3-5.3z"
}), "EditNote");
export {
  EditNote_default as default
};
//# sourceMappingURL=@mui_icons-material_EditNote.js.map
